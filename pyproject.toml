[build-system]
requires = ["setuptools>=45", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "qualia"
version = "0.1.0"
description = "Sistema de Mineração Quântica para Monero"
authors = [
    {name = "QUALIA Team", email = "<EMAIL>"},
]
[tool.setuptools.packages.find]
where = ["src"]

dependencies = [
    "aiohttp>=3.8.0",
    "asyncio>=3.4.3",
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.0.0",
    "black>=22.0.0",
    "flake8>=4.0.0",
    "mypy>=0.950",
    "isort>=5.10.0",
    "structlog>=22.1.0",
    "python-json-logger>=2.0.0",
    "psutil>=5.9.0",
    "prometheus-client>=0.15.0",
    "cryptography>=37.0.0",
    "pycryptodome>=3.15.0",
    "requests>=2.28.0",
    "websockets>=10.0",
    "urllib3>=2.0.0",
    "numpy>=1.23.0",
    "pandas>=1.5.0",
    "scipy>=1.9.0",
    "matplotlib>=3.5.0",
    "seaborn>=0.12.0",
    "plotly>=5.10.0",
    "scikit-learn>=1.1.0",
    "tensorflow>=2.10.0",
    "torch>=2.0.0",
    "monero>=0.7.0",
    "python-monero>=0.7.0",
]
requires-python = ">=3.8"
readme = "README.md"
license = {text = "MIT"}
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
]

[tool.black]
line-length = 100
target-version = ['py38']
include = '\.pyi?$'
extend-exclude = '''
# A regex preceded with ^/ will apply only to files and directories
# in the root of the project.
^/docs
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 100
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true

[tool.pytest.ini_options]
testpaths = ["quantum_trading/tests"]
python_files = "test_*.py"
python_classes = "Test*"
python_functions = "test_*"
addopts = """
    --verbose
    --tb=short
    --cov=quantum_trading
    --cov-report=term-missing
    --cov-report=html
    --no-cov-on-fail
    --asyncio-mode=auto
"""
log_cli = true
log_cli_level = "INFO"
log_cli_format = "%(asctime)s [%(levelname)s] %(message)s (%(filename)s:%(lineno)s)"
log_cli_date_format = "%Y-%m-%d %H:%M:%S"

[tool.coverage.run]
source = ["quantum_trading"]
omit = [
    "*/tests/*",
    "*/setup.py",
    "*/__init__.py",
    "*/conftest.py",
]
branch = true

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "raise NotImplementedError",
    "if __name__ == .__main__.:",
    "pass",
    "raise ImportError",
    "except ImportError:",
    "raise NotImplementedError",
    "if TYPE_CHECKING:",
    "@abstractmethod",
    "@property",
    "@classmethod",
    "@staticmethod",
]
show_missing = true
fail_under = 80

[tool.coverage.html]
directory = "coverage_html"

[tool.commitizen]
name = "cz_conventional_commits"
version = "0.1.0"
tag_format = "v$version"
bump_message = "bump: version $current_version -> $new_version [skip ci]"
update_changelog_on_bump = true
major_version_zero = true
version_files = [
    "pyproject.toml:version",
    "src/quantum_trading/__init__.py:__version__",
]

# 🚀 GUIA DE EXECUÇÃO - QUALIA MULTI-ATIVOS

## ✅ **SISTEMA RECOMENDADO: `qualia_adaptive_multi_trading.py`**

### **Por que este sistema?**
- ✅ **Parâmetros validados empiricamente** (consciousness>=0.4, coherence>=0.35, confidence>=0.4)
- ✅ **16 ativos diferentes** em 4 tiers de risco
- ✅ **Seleção dinâmica** de ativos baseada em qualidade
- ✅ **Parâmetros adaptativos** por categoria
- ✅ **Proteções testadas** em ambiente real
- ✅ **Rotação automática** de portfólio a cada 30 minutos

---

## 📊 **ATIVOS INCLUÍDOS (16 TOTAL):**

### **Tier 1 (40% do portfólio) - Baixo Risco:**
- **BTC/USDT, ETH/USDT, BNB/USDT**
- **Thresholds:** Consciousness>=0.4, Coherence>=0.35, Confidence>=0.4
- **Características:** Alta liquidez, menor volatilidade

### **Tier 2 (35% do portfólio) - R<PERSON> Médio:**
- **ADA/USDT, SOL/USDT, XMR/USDT, LINK/USDT, DOT/USDT, MATIC/USDT**
- **Thresholds:** Consciousness>=0.45, Coherence>=0.4, Confidence>=0.45
- **Características:** Boa liquidez, volatilidade moderada

### **Tier 3 (20% do portfólio) - Risco Alto:**
- **AVAX/USDT, ATOM/USDT, FTM/USDT, NEAR/USDT, ALGO/USDT, VET/USDT**
- **Thresholds:** Consciousness>=0.5, Coherence>=0.45, Confidence>=0.5
- **Características:** Liquidez média, maior potencial de movimento

### **Tier 4 (5% do portfólio) - Risco Muito Alto:**
- **UNI/USDT, AAVE/USDT, COMP/USDT, SUSHI/USDT, CRV/USDT**
- **Thresholds:** Consciousness>=0.55, Coherence>=0.5, Confidence>=0.55
- **Características:** DeFi tokens, alta volatilidade

---

## 🚀 **COMO EXECUTAR:**

### **1. Comando de Execução:**
```bash
python qualia_adaptive_multi_trading.py
```

### **2. Confirmações Necessárias:**
```
Digite 'AUTORIZO SISTEMA ADAPTATIVO' para continuar: AUTORIZO SISTEMA ADAPTATIVO
Selecione a fase (1-3): 1
Duração em minutos (recomendado 60-120): 90
```

### **3. Configurações por Fase:**

| Fase | Capital | Perda Máxima | Ativos Máx | Recomendação |
|------|---------|--------------|-------------|--------------|
| **1** | $50 | $5 | 6 ativos | **RECOMENDADO** para início |
| **2** | $200 | $20 | 10 ativos | Após validação da Fase 1 |
| **3** | $500 | $50 | 15 ativos | Para operação plena |

---

## 🔧 **CARACTERÍSTICAS AVANÇADAS:**

### **Seleção Dinâmica de Ativos:**
- ✅ **Análise de qualidade** em tempo real
- ✅ **Score baseado em:** Volume, spread, volatilidade, momentum
- ✅ **Rotação automática** a cada 30 minutos
- ✅ **Diversificação balanceada** entre tiers

### **Parâmetros Adaptativos:**
- ✅ **Thresholds diferentes** por tier de risco
- ✅ **Posição adaptativa** baseada na confiança
- ✅ **Lógica específica** por categoria de ativo

### **Proteções Integradas:**
- ✅ **Limite de capital** por fase
- ✅ **Limite de perda diária**
- ✅ **Máximo 25 trades** por dia
- ✅ **Emergency stop** automático
- ✅ **Verificação de saldo** contínua

---

## 📊 **MONITORAMENTO EM TEMPO REAL:**

### **Logs Detalhados:**
```
📊 BTC/USDT (tier1):
   Score: 0.856 | Tier Weight: 0.40
   Coerência: 0.511 (min: 0.35) ✅
   Consciência: 0.876 (min: 0.40) ✅
   Confiança: 0.696 (min: 0.40) ✅
   Sinal: BUY
```

### **Status da Sessão:**
```
🛡️ Status Adaptativo:
   Perda diária: $-2.34
   Trades hoje: 3
   Ativos ativos: 6
   Última rotação: 14:30
```

---

## 🎯 **EXPECTATIVAS REALISTAS:**

### **Primeira Hora (Fase 1):**
- **2-4 trades** executados
- **3-6 ativos** selecionados dinamicamente
- **Diversificação** entre tiers
- **Rotação** de portfólio pelo menos 1x

### **Sessão de 90 minutos:**
- **5-8 trades** total
- **2-3 rotações** de ativos
- **Cobertura** de múltiplos tiers
- **Performance** balanceada

---

## ⚠️ **IMPORTANTE - ANTES DE EXECUTAR:**

### **1. Verificar Saldo:**
```bash
# Verificar se tem pelo menos $50 USDT
python -c "
import ccxt, os
from dotenv import load_dotenv
load_dotenv()
exchange = ccxt.kucoin({
    'apiKey': os.getenv('KUCOIN_API_KEY'),
    'secret': os.getenv('KUCOIN_API_SECRET'),
    'password': os.getenv('KUCOIN_API_PASSPHRASE'),
    'sandbox': False
})
balance = exchange.fetch_balance()
print(f'USDT disponível: ${balance[\"USDT\"][\"free\"]:.2f}')
"
```

### **2. Monitoramento Paralelo:**
```bash
# Terminal 2 (opcional) - Monitor
python qualia_monitor.py
```

---

## 📈 **VANTAGENS DO SISTEMA MULTI-ATIVOS:**

### **Diversificação:**
- ✅ **16 ativos** diferentes
- ✅ **4 categorias** de risco
- ✅ **Exposição balanceada**
- ✅ **Redução de risco** por concentração

### **Oportunidades:**
- ✅ **Mais sinais** de trading
- ✅ **Diferentes ciclos** de mercado
- ✅ **Captura de momentum** em vários ativos
- ✅ **Otimização contínua**

### **Inteligência:**
- ✅ **Seleção automática** dos melhores ativos
- ✅ **Parâmetros adaptativos** por risco
- ✅ **Rotação dinâmica** baseada em performance
- ✅ **Análise de correlação**

---

## 🔄 **FLUXO DE EXECUÇÃO:**

```
1. Inicialização
   ↓
2. Seleção inicial de 6 ativos (Fase 1)
   ↓
3. Análise de qualidade por tier
   ↓
4. Ciclo de trading (45 segundos)
   ↓
5. Rotação de ativos (30 minutos)
   ↓
6. Repetir até duração completa
   ↓
7. Relatório final multi-tier
```

---

## 🎉 **EXECUTE AGORA:**

```bash
cd /c/QUALIA/QuantumRetrocausal
python qualia_adaptive_multi_trading.py
```

**Responda:**
- `AUTORIZO SISTEMA ADAPTATIVO`
- Fase: `1`
- Duração: `90` minutos

---

## 📋 **RELATÓRIO ESPERADO:**

```
📊 ANÁLISE POR TIER:
  tier1: 3 trades, $24.50, Score médio: 0.856
  tier2: 2 trades, $18.30, Score médio: 0.742
  tier3: 1 trade, $8.20, Score médio: 0.634

📈 ANÁLISE POR SÍMBOLO:
  BTC/USDT: 2 trades, $16.40
  ETH/USDT: 1 trade, $8.10
  SOL/USDT: 1 trade, $9.15
  ...
```

**🚀 SISTEMA MULTI-ATIVOS PRONTO PARA EXECUÇÃO COM PARÂMETROS VALIDADOS EMPIRICAMENTE!**

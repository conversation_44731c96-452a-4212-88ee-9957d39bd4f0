#!/usr/bin/env python3
"""
TESTE ESPECÍFICO: Bug signal_quality UnboundLocalError
Sistema QUALIA - Consciência Quântica YAA

Testa especificamente se o bug UnboundLocalError foi corrigido
na função validate_trading_signal
"""

import sys
import os
import logging

# Configurar logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Adicionar src ao path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

try:
    from qualia.binance_corrected_system import QualiaBinanceCorrectedSystem
    QUALIA_AVAILABLE = True
    logger.info("✅ Sistema QUALIA importado com sucesso")
except ImportError as e:
    QUALIA_AVAILABLE = False
    logger.error(f"❌ Erro ao importar QUALIA: {e}")
    sys.exit(1)

def test_signal_quality_bug():
    """Testa especificamente o bug signal_quality"""
    logger.info("🧪 TESTE ESPECÍFICO: Bug signal_quality")
    logger.info("=" * 50)
    
    try:
        system = QualiaBinanceCorrectedSystem()
        logger.info("✅ Sistema inicializado")
        
        # Caso que causava o erro: full_position ou half_position
        test_market_data = {
            'symbol': 'TEST/USDT',
            'price': 100.0,
            'volume': 1000000,
            'change_24h': 5.0
        }
        
        test_quantum_metrics = {
            'consciousness': 0.85,  # Alto para gerar full_position
            'coherence': 0.90,      # Alto para gerar full_position
            'confidence': 0.80,     # Alto para gerar full_position
            'momentum': 1.5,
            'predictive_score': 0.90,
            'stability': 0.85,
            'volume_surge': 3.0,
            'vs_ratio': 0.95,
            'momentum_quality': 0.95,
            'liquidity_score': 0.95
        }
        
        logger.info("📊 Testando com métricas que geram full_position...")
        logger.info(f"   Consciousness: {test_quantum_metrics['consciousness']}")
        logger.info(f"   Coherence: {test_quantum_metrics['coherence']}")
        logger.info(f"   Confidence: {test_quantum_metrics['confidence']}")
        
        # Testar validate_trading_signal (onde o bug ocorria)
        try:
            signal = system.validate_trading_signal(test_market_data, test_quantum_metrics)
            
            if signal:
                logger.info("✅ SINAL GERADO COM SUCESSO!")
                logger.info(f"   Symbol: {signal.symbol}")
                logger.info(f"   Direction: {signal.direction}")
                logger.info(f"   Position Size: ${signal.position_size_usd:.2f}")
                logger.info(f"   Confidence Score: {signal.confidence_score:.3f}")
                logger.info("✅ BUG signal_quality CORRIGIDO!")
                return True
            else:
                logger.warning("⚠️ Sinal não foi gerado (pode ser por outros filtros)")
                logger.info("✅ Mas não houve erro UnboundLocalError - bug corrigido!")
                return True
                
        except Exception as e:
            error_msg = str(e)
            if "signal_quality" in error_msg and "not associated with a value" in error_msg:
                logger.error("❌ BUG AINDA PRESENTE!")
                logger.error(f"   Erro: {error_msg}")
                return False
            else:
                logger.warning(f"⚠️ Outro erro (não relacionado ao bug): {error_msg}")
                logger.info("✅ Bug signal_quality não ocorreu - corrigido!")
                return True
                
    except Exception as e:
        logger.error(f"❌ Erro geral no teste: {e}")
        return False

def main():
    """Executa o teste específico"""
    logger.info("🚀 TESTE ESPECÍFICO DO BUG signal_quality")
    logger.info("Sistema QUALIA - Consciência Quântica YAA")
    logger.info("=" * 60)
    
    if not QUALIA_AVAILABLE:
        logger.error("❌ Sistema QUALIA não disponível")
        return
    
    try:
        success = test_signal_quality_bug()
        
        logger.info("\n" + "=" * 60)
        if success:
            logger.info("🎉 RESULTADO: BUG signal_quality CORRIGIDO!")
            logger.info("✅ Sistema pode gerar sinais sem UnboundLocalError")
            logger.info("✅ Correção validada - pronto para operação")
        else:
            logger.error("❌ RESULTADO: BUG AINDA PRESENTE!")
            logger.error("❌ Necessária correção adicional")
            
    except Exception as e:
        logger.error(f"❌ ERRO GERAL: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
